#!/usr/bin/env python3

import sys
import os

# Add the parent directory to the path so we can import from the amplify function
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'amplify', 'functions', 'call-function'))

from helpers.perplexity import call_perplexity_api

def test_new_perplexity():
    """Test the new perplexity.py implementation"""
    
    prompt = """a paper is being written about "Gender equality". find up to 1 original sources (no duplicates) which would have been used to generate the excerpt below. provide me with a url & title of the source and specify if it is a research paper or not. the fields you provide should be for a source that relatively matches the topic provided. give it in this format and do not provide any extra information besides this
URL: [url] 
 TITLE: [title]
 Research paper: [yes/no]

excerpt: Moving on to the speech Emma delivered to launch the campaign back in September (https://www.youtube.com/watch?v=gkjW9PZBRfk). There were many layers of this speech that effectively highlight the campaign, which set it up to go viral once it was launched. The video from the United Nations' YouTube has only 458,495 views. However, there is a different video from YouTube user normaljean2, which has 6,243,504 views and on the video uploaded by HeForShe there are 1,418,431 views. Clearly, this video has been extremely effective in spreading the word for the campaign. The use of social media absolutely helped push this video to become as popular as it was. First off, the use of YouTube allowed a digital platform for the video to be uploaded to. Before YouTube was created many speeches like this had to be broadcast solely on television. In my opinion, YouTube is one of the better forms of social media"""
    
    print("=== Testing new perplexity.py implementation ===")
    
    try:
        result = call_perplexity_api(prompt)
        print(f"Success! Result length: {len(result)}")
        print(f"Result: {result}")
        return True
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_new_perplexity()
    sys.exit(0 if success else 1)
