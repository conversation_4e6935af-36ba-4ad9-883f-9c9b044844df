##### PERPLEXITY USAGE FOR INITIAL METADATA #####
"""
Perplexity API integration for CiteAI
Handles API calls to Perplexity's Sonar model for academic source discovery
"""

import requests
import sys
import os

def call_perplexity_api(prompt: str) -> str:
    """
    Call Perplexity API to find academic sources

    Args:
        prompt (str): The search prompt for finding academic sources

    Returns:
        str: Raw response content from Perplexity API, or empty string on error

    Note:
        Uses simple text format instead of JSON schema for better compatibility
        with Perplexity's Sonar API. The response is parsed by downstream functions.
    """
    endpoint = "https://api.perplexity.ai/chat/completions"

    # Get API key from environment variable, fallback to hardcoded for backward compatibility
    api_key = os.environ.get("PERPLEXITY_API_KEY", "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")

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    # Use simple format that matches the working old main.py implementation
    # This format is fully compatible with Perplexity's Sonar API
    payload = {
        "model": "sonar",
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant that finds academic sources. Respond in the exact format requested."
            },
            {
                "role": "user",
                "content": prompt
            }
        ]
    }

    return _make_api_request(endpoint, headers, payload)


def _make_api_request(endpoint, headers, payload):
    """
    Helper function to make the actual API request
    """

    # Ensure payload is properly serializable
    import json
    try:
        json.dumps(payload)
        print(f'CITEAI DEBUG: Payload is valid JSON', file=sys.stderr)
    except Exception as json_error:
        print(f'CITEAI ERROR: Payload is not valid JSON: {json_error}', file=sys.stderr)
        return ""

    # Extract prompt for logging
    prompt = payload.get("messages", [{}])[-1].get("content", "")

    try:
        print(f'CITEAI DEBUG: Calling Perplexity API with prompt: {prompt[:200]}...', file=sys.stderr)
        print(f'CITEAI DEBUG: Full prompt length: {len(prompt)}', file=sys.stderr)
        print(f'CITEAI DEBUG: Endpoint: {endpoint}', file=sys.stderr)
        print(f'CITEAI DEBUG: Headers: {headers}', file=sys.stderr)
        print(f'CITEAI DEBUG: Payload model: {payload["model"]}', file=sys.stderr)
        print(f'CITEAI DEBUG: Payload messages count: {len(payload["messages"])}', file=sys.stderr)

        # Use more robust request settings for Lambda environment
        resp = requests.post(
            endpoint,
            headers=headers,
            json=payload,
            timeout=60,
            verify=True,  # Ensure SSL verification
            allow_redirects=True
        )

        print(f'CITEAI DEBUG: Response status code: {resp.status_code}', file=sys.stderr)
        print(f'CITEAI DEBUG: Response headers: {dict(resp.headers)}', file=sys.stderr)

        if resp.status_code != 200:
            print(f'CITEAI DEBUG: Response text: {resp.text}', file=sys.stderr)

        resp.raise_for_status()
        data = resp.json()
        content = data["choices"][0]["message"]["content"]

        print(f'CITEAI DEBUG: Perplexity API response length: {len(content)}', file=sys.stderr)
        print(f'CITEAI DEBUG: Perplexity API response: {content}', file=sys.stderr)

        # Return the raw content string - let parse_multiple_sources handle the parsing
        return content

    except Exception as e:
        print(f'CITEAI ERROR: Perplexity API call failed: {e}', file=sys.stderr)
        print(f'CITEAI ERROR: Exception type: {type(e).__name__}', file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        return ""


def test_perplexity_connection():
    """
    Simple test function to verify Perplexity API connectivity
    Returns True if API is working, False otherwise
    """
    try:
        simple_prompt = "What is AI?"
        result = call_perplexity_api(simple_prompt)
        return len(result) > 0
    except Exception as e:
        print(f'CITEAI ERROR: Perplexity connection test failed: {e}', file=sys.stderr)
        return False
