from datetime import datetime
import re
import sys
import spacy
import requests
import time
from bs4 import BeautifulSoup
import json
from dateutil import parser as dateparser
import uuid
import argparse
import math
import tempfile
from typing import Optional, Dict, Any


class CitationCandidate:
    def __init__(self, sentence, needs_citation=False):
        self.sentence = sentence
        self.needs_citation = needs_citation

    def flag(self):
        self.needs_citation = True

nlp = spacy.load("en_core_web_sm")
doc = nlp("I have sentence 1. Now we have sentence 2, but it has a comma so is it considered its own?")
flagged_sentences = []
TRIGGER_KEYWORDS = {
    # Statistical / Quantitative
    "percent", "%", "ratio", "rate", "majority", "average", "mean", "median",
    "proportion", "increase", "decrease", "growth", "decline",

    # Academic / Research Context
    "study", "studies", "research", "report", "survey", "analysis", "evidence",
    "experiment", "results", "data", "paper", "meta-analysis", "dataset",
    "trial", "theory", "literature", "framework", "model", "regression",
    "statistically", "significant", "odds ratio", "p-value", "methodology",
    "discussion", "limitations", "implications", "systematic", "review",

    # Attribution / Claims
    "according", "claims", "found", "suggests", "concludes", "indicates",
    "shows", "reported", "revealed", "published", "mentions", "responded",
    "findings",

    # Phrases
    "according to", "studies show", "research indicates", "data suggests",

    
}

#sentence flagging
for sent in doc.sents:
        pos_candidate = CitationCandidate(sent, needs_citation=False)
        if any(phrase in sent.text.lower() for phrase in TRIGGER_KEYWORDS): #check for phrases
            pos_candidate.flag()
            flagged_sentences.append(pos_candidate)
    
    
##### WEB SCRAPING TOOLS #####

def clean_date_string(s: str) -> str:
    # Remove prefixes like 'Date:', 'Published:', etc.
    s = re.sub(r"^(date|published|on)[:\-\s]+", "", s.strip(), flags=re.IGNORECASE)
    return s.strip()

def _parse_author_name(name: str, is_last_author=False) -> dict:
    # Remove "by" prefix if present
    name = re.sub(r"^by\s*", "", name, flags=re.IGNORECASE)
    name = re.sub(r"\s*[\|‐–-]\s*.*$", "", name)
    name = re.sub(r"\b(BBC|CNN|Times|News|Daily|Post)$", "", name).strip()
    parts = name.split()
    if len(parts) == 1:
        return {"family": parts[0]}
    
    # For the last author, don't add periods to initials
    given = " ".join(parts[:-1])
    if is_last_author:
        # Remove periods from initials for last author
        given = re.sub(r"\.", "", given)
    
    return {"given": given, "family": parts[-1]}

def build_csl_json(meta: dict, idx=None) -> dict | None:
    # Skip if no title (will cause "None" entries)
    if not meta or not meta.get("title"):
        return None
    csl = {
        "id": meta.get("id") or f"ref{idx or uuid.uuid4()}",
        "type": meta.get("type", "article-journal"),
        "title": meta.get("title"),
        "URL": meta.get("url")
    }

    max_authors = 5

    # Handle authors from OpenAlex/Crossref (list of strings or dicts)
    authors = meta.get("authors")
    if authors:
        csl_authors = []
        for i, a in enumerate(authors):
            is_last = (i == len(authors) - 1) or (i == max_authors - 1)
            if isinstance(a, dict) and a.get("name"):
                csl_authors.append(_parse_author_name(a["name"], is_last_author=is_last))
            elif isinstance(a, dict) and a.get("given") and a.get("family"):
                csl_authors.append({"given": a["given"], "family": a["family"]})
            elif isinstance(a, str):
                # Split string into given/family (best effort)
                parts = a.strip().split()
                if len(parts) == 1:
                    csl_authors.append({"family": parts[0]})
                elif len(parts) > 1:
                    csl_authors.append({"given": " ".join(parts[:-1]), "family": parts[-1]})
        csl["author"] = csl_authors[:max_authors]
    elif meta.get("author"):
        csl["author"] = [_parse_author_name(meta["author"], is_last_author=True)]

    # date / year
    date_val = meta.get("date") or meta.get("year")
    if date_val:
        s = str(date_val)
        if re.fullmatch(r"\d{4}", s):             # year only
            parts = [int(s)]
            csl["issued"] = {"date-parts": [parts]}
        else:
            cleaned_s = clean_date_string(s)
            try:
                dt = dateparser.parse(cleaned_s)
                parts = [dt.year]
                if dt.month:
                    parts.append(dt.month)
                if dt.day:
                    parts.append(dt.day)
                csl["issued"] = {"date-parts": [parts]}
            except Exception:
                pass

    # container title - always prioritize 'venue' if present
    if meta.get("venue"):
        csl["container-title"] = meta["venue"]
    elif meta.get("site_name"):
        csl["container-title"] = meta["site_name"]
    # Do not use DOI as container-title
    if meta.get("DOI"):
        csl["DOI"] = meta["DOI"]
    elif meta.get("externalIds") and isinstance(meta["externalIds"], dict):
        doi = meta["externalIds"].get("DOI")
        if doi:
            csl["DOI"] = doi

    return csl

def deduplicate_csl_items(csl_items):
    seen = set()
    deduped = []
    for csl in csl_items:
        key = csl.get("DOI") or csl.get("URL") or json.dumps(csl, sort_keys=True)
        if key not in seen:
            seen.add(key)
            deduped.append(csl)
    return deduped

#metadata 
def extract_article_metadata(url: str, idx=None) -> tuple[dict, dict]:
    headers = {
        "User-Agent": "CiteAI/1.0"
    }

    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
    except requests.RequestException as e:
        print("Request failed:", e)
        return {}, {}

    soup = BeautifulSoup(response.text, "html.parser")


    meta = {
        "title": get_title(soup),
        "author": get_author(soup),
        "date":      get_date(soup),
        "url":       url,
        "site_name": getattr(soup.find("meta", property="og:site_name"), "content", None)
    }
    csl = build_csl_json(meta, idx)
    return meta, csl if csl else {}


def get_title(soup_obj):
    meta_title = soup_obj.find("meta", property="og:title")
    # return title if it exists
    if meta_title and meta_title.get("content"):
        return meta_title["content"]

    # try to find the title looking for the other tags
    possible_titles = soup_obj.find_all(["h1", "h2", "div", "span"])

    for tag in possible_titles:
        classes = tag.get("class", [])  # getting class present in tag
        # if we find any title or headline 
        if any("title" in c.lower() or "headline" in c.lower() for c in classes):
            text = tag.get_text(strip=True)
            return text

    if soup_obj.title:
        return soup_obj.title.get_text(strip=True)
    return None


BAD_AUTHOR_VALUES = {
    "watch live", "read more", "staff", "editor", "editors",
    "live", "breaking news", "by", "byline", "guest author"
}

def extract_clean_name(author_value):
    if not author_value:
        return None

    text = author_value.strip()
    # Remove "by" prefix more aggressively
    text = re.sub(r"^by\s*", "", text, flags=re.IGNORECASE)
    if "by" in text.lower():
        parts = re.split(r"\bby\b", text, flags=re.IGNORECASE)
        if len(parts) > 1:
            text = parts[1].strip()

    # Validate that this looks like a real author name
    def is_valid_author_name(name):
        if not name or len(name.strip()) < 2:
            return False
        
        # Skip if contains numbers (like "Date: Sept. 14" -> "Date S.")
        if any(char.isdigit() for char in name):
            return False
        
        # Skip if contains common date-related words
        date_words = ['date', 'published', 'updated', 'created', 'modified', 'sept', 'oct', 'nov', 'dec', 'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug']
        if any(word in name.lower() for word in date_words):
            return False
        
        # Skip if it's just a single letter or very short
        if len(name.strip()) <= 2:
            return False
        
        # Skip if it contains special characters that aren't typical in names
        if re.search(r'[^\w\s\-\.]', name):
            return False
        
        # Must contain at least one letter
        if not re.search(r'[a-zA-Z]', name):
            return False
        
        return True

    doc = nlp(text)
    for entry in doc.ents:
        if entry.label_ == "PERSON":
            person_name = entry.text.strip()
            if is_valid_author_name(person_name):
                return person_name

    # If spaCy doesn't find a person, validate the cleaned text
    if is_valid_author_name(text):
        return text
    
    return None


def get_author(soup_obj):
    nbc_div = soup_obj.find("div", class_="article-inline-byline")
    if nbc_div:
        # find the inner <a> (author link)
        author_link = nbc_div.find("span", class_="byline-name")
        if author_link is not None:
            a_tag = author_link.find("a") if author_link else None
            if a_tag and a_tag.get_text(strip=True):
                # The byline text is already “Firstname Lastname”
                return a_tag.get_text(strip=True)
            
    # 1. JSON-LD: Only accept if "@type": "Person"
    for script in soup_obj.find_all("script", type="application/ld+json"):
        try:
            data = json.loads(script.string)
            if isinstance(data, list):
                for item in data:
                    if isinstance(item, dict) and "author" in item:
                        author = item["author"]
                        if isinstance(author, dict) and author.get("@type", "").lower() == "person" and author.get("name"):
                            cleaned = extract_clean_name(author["name"])
                            if cleaned:
                                return cleaned
            elif isinstance(data, dict) and "author" in data:
                author = data["author"]
                if isinstance(author, dict) and author.get("@type", "").lower() == "person" and author.get("name"):
                    cleaned = extract_clean_name(author["name"])
                    if cleaned:
                        return cleaned
        except Exception:
            continue

    # 2. Look near <time> tag
    time_tag = soup_obj.find("time")
    if time_tag:
        for parent in [time_tag.parent, getattr(time_tag.parent, "parent", None)]:
            if not parent:
                continue
            for tag in parent.find_all(["span", "div", "p"], recursive=False):
                text = tag.get_text(strip=True)
                cleaned = extract_clean_name(text)
                if cleaned:
                    return cleaned

    # 3. <meta name="author">
    meta_author = soup_obj.find("meta", attrs={"name": "author"})
    if meta_author:
        text = meta_author.get("content", "").strip()
        cleaned = extract_clean_name(text)
        if cleaned:
            return cleaned

    # 4. Class-based: "author" or "byline"
    for tag in soup_obj.find_all(class_=lambda c: c and ("author" in c.lower() or "byline" in c.lower())):
        text = tag.get_text(strip=True)
        cleaned = extract_clean_name(text)
        if cleaned:
            return cleaned

    # 5. Links with "/author/", "/by/", etc.
    for tag in soup_obj.find_all("a", href=lambda h: h and any(kw in h.lower() for kw in ["/author/", "/by/", "writer", "people", "profile"])):
        text = tag.get_text(strip=True)
        cleaned = extract_clean_name(text)
        if cleaned:
            return cleaned

    return None


def get_date(soup_obj):
    # 1. Check JSON-LD for datePublished
    ld_json_scripts = soup_obj.find_all("script", type="application/ld+json")
    for script in ld_json_scripts:
        try:
            data = json.loads(script.string)
            if isinstance(data, list):
                for item in data:
                    if isinstance(item, dict) and "datePublished" in item:
                        return item["datePublished"]
            elif isinstance(data, dict) and "datePublished" in data:
                return data["datePublished"]
        except Exception:
            continue

    # 2. Check meta tag
    meta_date = soup_obj.find("meta", property="article:published_time")
    if meta_date and meta_date.get("content"):
        return meta_date["content"]

    # 3. Look for class names containing "date"
    date_candidates = soup_obj.find_all(class_=lambda c: c and "date" in c.lower())
    for tag in date_candidates:
        text = tag.get_text(strip=True)
        if text and any(char.isdigit() for char in text):
            return text

    return None

    
#url = "https://www.nbcnews.com/news/us-news/family-boulder-attack-suspect-taken-ice-custody-dhs-chief-krisi-noem-s-rcna210711" #url from perplexity
#meta1, csl_json1 = extract_article_metadata(url)


##### SEMANTIC SCHOLAR TOOLS#####



OPENALEX_BASE_URL = "https://api.openalex.org/works"
CROSSREF_BASE_URL = "https://api.crossref.org/works"
NCBI_IDCONV_URL = "https://www.ncbi.nlm.nih.gov/pmc/utils/idconv/v1.0/"

def extract_doi_from_url(url: str, email: str = "<EMAIL>") -> Optional[str]:
    doi_match = re.search(r"(10\.\d{4,9}/[-._;()/:A-Z0-9]+)", url, re.IGNORECASE)
    if doi_match:
        return doi_match.group(1).strip()
    pmid_match = re.search(r"pubmed\.ncbi\.nlm\.nih\.gov/(\d+)", url)
    if pmid_match:
        pmid = pmid_match.group(1)
        return _get_doi_from_ncbi(pmid, email)
    pmcid_match = re.search(r"pmc/articles/(PMC\d+)", url, re.IGNORECASE)
    if pmcid_match:
        pmcid = pmcid_match.group(1)
        return _get_doi_from_ncbi(pmcid, email)
    return None

def _get_doi_from_ncbi(id_str: str, email: str) -> Optional[str]:
    params = {
        "tool": "citeai",
        "email": email,
        "ids": id_str,
        "format": "json"
    }
    resp = requests.get(NCBI_IDCONV_URL, params=params, timeout=10)
    if resp.ok:
        data = resp.json().get("records", [])
        if data and data[0].get("doi"):
            return data[0]["doi"]
    return None

def query_openalex_by_title(title: str, max_results: int = 5) -> Optional[Dict[str, Any]]:
    try:
        params = {
            "search": title,
            "per-page": max_results
        }
        resp = requests.get(OPENALEX_BASE_URL, params=params, timeout=10)
        if not resp.ok:
            return None
        results = resp.json().get("results", [])
        if not results:
            return None
        work = results[0]

        # Safely get venue with fallbacks
        venue = None
        if work.get("host_venue"):
            venue = work.get("host_venue", {}).get("display_name")
        if not venue and work.get("primary_location"):
            venue = work.get("primary_location", {}).get("source", {}).get("display_name")

        # Safely get URL
        url = None
        if work.get("primary_location"):
            url = work.get("primary_location", {}).get("landing_page_url")

        return {
            "title": work.get("display_name"),
            "authors": [a["author"]["display_name"] for a in work.get("authorships", []) if a.get("author")],
            "year": work.get("publication_year"),
            "venue": venue,
            "url": url,
            "externalIds": work.get("ids", {})
        }
    except Exception as e:
        print(f"OpenAlex query failed for title '{title}': {e}", file=sys.stderr)
        return None

def query_crossref_by_doi(doi: str) -> Optional[Dict[str, Any]]:
    try:
        url = f"{CROSSREF_BASE_URL}/{doi}"
        resp = requests.get(url, timeout=10)
        if not resp.ok:
            return None
        data = resp.json().get("message", {})
        return {
            "title": data.get("title", [""])[0],
            "authors": [
                f"{a.get('given', '')} {a.get('family', '')}".strip()
                for a in data.get("author", [])
            ],
            "year": data.get("issued", {}).get("date-parts", [[None]])[0][0],
            "venue": data.get("container-title", [""])[0],
            "url": data.get("URL"),
            "externalIds": {"DOI": data.get("DOI")}
        }
    except Exception as e:
        print(f"Crossref query failed for DOI '{doi}': {e}", file=sys.stderr)
        return None

def fetch_paper_metadata(title: str, url: str) -> Optional[Dict[str, Any]]:
    meta = query_openalex_by_title(title)
    if meta and meta.get("title"):
        return meta
    doi = extract_doi_from_url(url)
    if doi:
        return query_crossref_by_doi(doi)
    return None

# Comment out or remove the old semantic_scholar_search function
# def semantic_scholar_search(...):
#     ...

# In the main citation extraction loop, replace:
# if is_paper:
#     _, csl = semantic_scholar_search(title, idx=i)
# else:
#     _, csl = extract_article_metadata(url, idx=i)
# if csl and csl.get("title"):
#     csl_items.append(csl)




##### PERPLEXITY USAGE FOR INITIAL METADATA #####

def call_perplexity_api(prompt: str): #-> tuple[str,str, bool]:
    endpoint = "https://api.perplexity.ai/chat/completions"
    api_key = "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"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    json_schema = {
        "schema": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "url": {"type": "string"},
                    "title": {"type": "string"},
                    "is_paper": {"type": "boolean"}
                },
                "required": ["url", "title", "is_paper"]
            }
        }
    }

    payload = {
        "model": "sonar",
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant that finds academic sources. Respond in the exact format requested."
            },
            {
                "role": "user",
                "content": prompt
            },
        ]
    }
    

    try:
        resp = requests.post(endpoint, headers=headers, json=payload, timeout=60)
        resp.raise_for_status()
        data = resp.json()
        content = data["choices"][0]["message"]["content"]
        # Return the raw content string - let parse_multiple_sources handle the parsing
        return content
    except Exception as e:
        print(f'CITEAI ERROR: Perplexity API call failed: {e}', file=sys.stderr)
        return ""

def parse_json_input():
    import sys, json
    if not sys.stdin.isatty():
        data = sys.stdin.read()
        return json.loads(data)
    return None

# ---- Citation Calculation Tunables ----
PLAN_TOTAL_CAP   = {"free": 1, "plus": 20, "pro": 40}
PLAN_REQUEST_CAP = {"free": 1, "plus": 12, "pro": 16}
PLAN_MULT        = {"free": 1.0, "plus": 1.0, "pro": 1.2}
GLOBAL_MAX = 50

# --- regex helpers ---
URL_RE   = re.compile(r"\bhttps?://[^\s)]+", re.I)
DOI_RE   = re.compile(r"\b10\.\d{4,9}/\S+\b", re.I)
PAREN_CITE_RE = re.compile(r"\([A-Z][A-Za-z-]+(?:\s+et\s+al\.)?(?:,\s*\d{4}[a-z]?)\)", re.U)

def _word_count_fast(text: str) -> int:
    return len(re.findall(r"\b\w+\b", text))

def _estimate_pages_from_words(total_words: int, spacing: str = "double") -> float:
    """500 wpp = single, 250 wpp = double (default)."""
    spacing = (spacing or "double").lower()
    wpp = 500 if spacing == "single" else 250
    return max(1.0, total_words / float(wpp))

def _research_intensity_score(text: str) -> float:
    """0..1 score from trigger words + year mentions."""
    t = text.lower()
    trigger_hits = sum(t.count(w) for w in TRIGGER_KEYWORDS)
    trigger = min(trigger_hits / 12.0, 1.0)
    year_hits = len(re.findall(r"\b(19[5-9]\d|20[0-2]\d)\b", t))
    years = min(year_hits / 10.0, 1.0)
    return max(0.0, min(0.65 * trigger + 0.35 * years, 1.0))

def _density_from_intensity(intensity: float) -> float:
    """Use band [1.6, 2.6] citations per double-spaced page, center ≈2.1/page."""
    return 1.6 + (2.6 - 1.6) * intensity

def _source_demand_signals(text: str, explicit_quote_count: Optional[int] = None) -> int:
    """Minimum number of distinct sources implied by quotes/URLs/parentheses."""
    if explicit_quote_count is not None:
        quotes = max(0, int(explicit_quote_count))
    else:
        quotes = text.count('"') // 2

    urls = set(URL_RE.findall(text))
    dois = set(DOI_RE.findall(text))
    paren_cites = len(PAREN_CITE_RE.findall(text))

    demand_score = len(urls) + len(dois) + paren_cites + math.ceil(0.6 * quotes)
    if (quotes > 0) or (len(urls) + len(dois) > 0) or (paren_cites > 0):
        demand_score = max(demand_score, 2)
    return demand_score

def compute_paper_target(plan: str,
                         *,
                         total_word_count: int,
                         spacing: str = "double",
                         intensity_hint: Optional[float] = None,
                         sample_text: Optional[str] = None) -> int:
    plan = (plan or "free").lower()
    if plan == "free":
        return 1

    pages = _estimate_pages_from_words(total_word_count, spacing)
    if intensity_hint is None:
        intensity = _research_intensity_score(sample_text or "")
        if sample_text is None:
            intensity = 0.4
    else:
        intensity = max(0.0, min(float(intensity_hint), 1.0))

    density = _density_from_intensity(intensity)
    base_total = pages * density
    scaled = math.ceil(base_total * PLAN_MULT.get(plan, 1.0))

    total_cap = PLAN_TOTAL_CAP.get(plan, 20 if plan == "plus" else 40)
    return int(min(scaled, total_cap, GLOBAL_MAX))

def num_citations(plan: str,
                  content_chunk: str,
                  *,
                  paper_target: Optional[int] = None,
                  total_word_count: Optional[int] = None,
                  spacing: str = "double",
                  already_cited: int = 0,
                  intensity_hint: Optional[float] = None,
                  explicit_quote_count: Optional[int] = None) -> int:
    """
    Decide how many citations to generate for THIS chunk.
    Uses max(length_target, demand_target) and clamps to remaining budget & plan caps.
    """
    plan = (plan or "free").lower()

    if plan == "free":
        return 1

    if paper_target is None and total_word_count is not None:
        paper_target = compute_paper_target(plan,
                                            total_word_count=total_word_count,
                                            spacing=spacing,
                                            intensity_hint=intensity_hint,
                                            sample_text=content_chunk)

    total_cap = PLAN_TOTAL_CAP.get(plan, 20 if plan == "plus" else 40)
    if paper_target is None:
        paper_target = total_cap

    remaining = max(0, paper_target - already_cited)
    if remaining == 0:
        return 0

    wc = _word_count_fast(content_chunk)
    pages_local = _estimate_pages_from_words(wc, spacing)
    local_intensity = _research_intensity_score(content_chunk) if content_chunk else (intensity_hint or 0.4)
    density_local = _density_from_intensity(local_intensity)
    length_target = math.ceil(pages_local * density_local * PLAN_MULT.get(plan, 1.0))

    demand_target = _source_demand_signals(content_chunk, explicit_quote_count)

    proposed = max(length_target, demand_target)
    request_cap = PLAN_REQUEST_CAP.get(plan, 12)
    per_request = min(proposed, request_cap, remaining)

    return int(max(1 if wc > 0 and remaining > 0 else 0, per_request))


#------------ MAIN DRIVER --------------
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="CiteAI Citation Generator")
    parser.add_argument('--input_json', type=str, help='Path to JSON file with title, content, citation_format, plan')
    parser.add_argument('--output_mode', type=str, choices=['text', 'pdf', 'html'], help='Output mode: text, pdf, or html')
    args = parser.parse_args()

    # Prefer stdin JSON, fallback to file if provided
    input_data = parse_json_input()
    if not input_data and args.input_json:
        with open(args.input_json, 'r', encoding='utf-8') as f:
            input_data = json.load(f)
    if not input_data:
        print("No input data provided. Use stdin or --input_json.")
        sys.exit(1)

    title = input_data.get('title', '')
    content = input_data.get('content', '')
    citation_format = input_data.get('citation_format', 'mla').lower()
    plan = input_data.get('plan', 'free').lower()
    output_mode = args.output_mode or ('pdf' if plan in ['plus', 'pro'] else 'text')
    format_only = input_data.get('format_only', False)
    csl_items = input_data.get('csl_items', [])

    # Check if we're just formatting existing citations
    if format_only and csl_items:
        # Just format the provided CSL items
        csl_items = deduplicate_csl_items(csl_items)
        
        # Output logic
        if output_mode == 'text':
            from filegen import format_citations_text
            formatted = format_citations_text(csl_items, citation_format)
            print(formatted)
        elif output_mode == 'html':
            from filegen import generate_html, sort_csl_items, build_formatted_entries, fix_missing_fields, clean_author_names, abbreviate_given_names
            valid_items = [item for item in csl_items if isinstance(item, dict) and item.get("title")]
            valid_items = fix_missing_fields(valid_items, citation_format)
            valid_items = clean_author_names(valid_items)
            if citation_format.lower() == "apa":
                valid_items = abbreviate_given_names(valid_items)
            sorted_items = sort_csl_items(valid_items, citation_format)
            entries = build_formatted_entries(sorted_items, citation_format)
            html = generate_html(entries, citation_format, sorted_items)
            print(html)
        else:
            from filegen import generate_citations_pdf
            pdf_path = generate_citations_pdf(csl_items, citation_format)
            print(json.dumps({"pdf_path": pdf_path}))
        
        sys.exit(0)
    
    # Run citation logic (replaces old file input)
    doc = nlp(content)
    sent_list = list(doc.sents)
    flagged_idxs=[]
    for i,sent in enumerate(sent_list):
        txt=sent.text.strip()
        if not txt: continue
        if any(phrase in txt.lower() for phrase in TRIGGER_KEYWORDS):
            flagged_idxs.append(i)
    # Debug: print all flagged sentences
    csl_items = []
    topic = title
    
    # Calculate total word count for the paper
    total_word_count = _word_count_fast(content)
    
    # Calculate paper target for citation planning
    paper_target = compute_paper_target(plan, total_word_count=total_word_count, sample_text=content)
    
    # For free plan, always return 1 citation
    if plan == 'free':
        total_citations = 1
    else:
        # For plus/pro plans, calculate based on content
        total_citations = num_citations(plan, content, paper_target=paper_target, total_word_count=total_word_count)
    
    if plan == 'pro' and flagged_idxs:
        # Only for pro plan: process flagged sentences
        already_cited = 0
        for idx, i in enumerate(flagged_idxs):
            ctx = []
            for j in (i-1, i, i+1):
                if 0 <= j < len(sent_list):
                    ctx.append(sent_list[j].text.strip())
            words = " ".join(ctx).split()
            context_text = " ".join(words[:500])
            
            # Calculate citations for this specific chunk
            chunk_citations = num_citations(plan, context_text, 
                                          paper_target=paper_target, 
                                          total_word_count=total_word_count,
                                          already_cited=already_cited)
            
            if chunk_citations == 0:
                print(f'CITEAI INFO: No more citations needed for chunk {idx+1}', file=sys.stderr)
                continue
                
            print(f'PERPLEXITY CONTEXT_TEXT [{idx+1}][Index {i}]: {context_text}', file=sys.stderr, flush=True)
            prompt = (
                f"a paper is being written about \"{topic}\". find up to {chunk_citations} original sources (no duplicates) which would have been used to generate the excerpt below. provide me with a url & title of the source and specify if it is a research paper or not. the fields you provide should be for a source that relatively matches the topic provided. give it in this format and do not provide any extra information besides this"
                "URL: [url] \n TITLE: [title]\n Research paper: [yes/no]\n"
                f"excerpt: {context_text}\n"
            )
            # Print the exact prompt being sent to Perplexity
            print('\n========== PERPLEXITY PROMPT SENT ==========', file=sys.stderr)
            print(f'Title: {topic}', file=sys.stderr)
            print(f'Excerpt: {context_text}', file=sys.stderr)
            print(f'chunk_citations: {chunk_citations}', file=sys.stderr)
            print('Prompt:', prompt, file=sys.stderr)
            print('============================================\n', file=sys.stderr)
            output = call_perplexity_api(prompt)
            # Print the exact JSON returned by Perplexity
            print('\n========== PERPLEXITY RAW JSON RETURNED ==========', file=sys.stderr)
            print(json.dumps(output, indent=2), file=sys.stderr)
            print('============================================\n', file=sys.stderr)
            # Save raw Perplexity output to a temp file for inspection
            with tempfile.NamedTemporaryFile('w', delete=False, suffix='.json') as tmpf:
                json.dump(output, tmpf, indent=2)
                print(f'PERPLEXITY RAW OUTPUT SAVED TO: {tmpf.name}', file=sys.stderr)
            # Robustly parse all sources from Perplexity output
            def parse_multiple_sources(output):
                # If output is already a list of dicts (expected JSON format)
                if isinstance(output, list):
                    return output
                # If output is a dict with url/title/is_paper, wrap in a list
                if isinstance(output, dict) and output.get('url'):
                    return [output]
                # If output is a dict with 'results' key
                if isinstance(output, dict) and 'results' in output and isinstance(output['results'], list):
                    return output['results']
                # Fallback: if output is a string, try to parse as JSON
                if isinstance(output, str):
                    try:
                        parsed = json.loads(output)
                        if isinstance(parsed, list):
                            return parsed
                        elif isinstance(parsed, dict) and parsed.get('url'):
                            return [parsed]
                    except json.JSONDecodeError:
                        # If JSON parsing fails, try the old text parsing as fallback
                        blocks = [b.strip() for b in re.split(r'\n\s*\n|(?=URL:)', output) if b.strip()]
                        results = []
                        for block in blocks:
                            url = re.search(r'URL:\s*(.+)', block)
                            title = re.search(r'TITLE:\s*(.+)', block)
                            is_paper = re.search(r'Research paper:\s*(yes|no)', block, re.IGNORECASE)
                            if url and title and is_paper:
                                results.append({
                                    'url': url.group(1).strip(),
                                    'title': title.group(1).strip(),
                                    'is_paper': is_paper.group(1).strip().lower() == 'yes'
                                })
                        return results
                return []
            sources = parse_multiple_sources(output)
            for src in sources:
                url = src.get('url', '')
                title = src.get('title', '')
                is_paper = src.get('is_paper', None)
                def is_invalid(val):
                    return not val or str(val).strip().lower() in ['n/a', 'na']
                if is_invalid(url) or is_invalid(title) or is_paper is None:
                    print(f'CITEAI WARNING: Skipping blank or N/A Perplexity output for flagged sentence {idx+1} [Index {i}].', file=sys.stderr)
                    continue
                if is_paper:
                    meta = fetch_paper_metadata(title, url)
                    csl = build_csl_json(meta, idx=i) if meta else None
                    if csl:
                        print(f'CSL JSON (OpenAlex/Crossref) for "{title}":', file=sys.stderr)
                        print(json.dumps(csl, indent=2), file=sys.stderr)
                else:
                    _, csl = extract_article_metadata(url, idx=i)
                if csl and csl.get("title"):
                    csl_items.append(csl)
                    already_cited += 1
    else:
        # For free and plus plans: use the entire content field
        context_text = content.strip()
        words = context_text.split()
        if plan == 'plus':
            context_text = " ".join(words[:500])
        else:
            context_text = " ".join(words[:150])
        print(f'PERPLEXITY CONTEXT_TEXT [Full Content]: {context_text}', file=sys.stderr, flush=True)
        prompt = (
            f"a paper is being written about \"{topic}\". find up to {total_citations} original sources (no duplicates) which would have been used to generate the excerpt below. provide me with a url & title of the source and specify if it is a research paper or not. the fields you provide should be for a source that relatively matches the topic provided. give it in this format and do not provide any extra information besides this"
            "URL: [url] \n TITLE: [title]\n Research paper: [yes/no]\n"
            f"excerpt: {context_text}\n"
        )
        # Print the exact prompt being sent to Perplexity
        print('\n========== PERPLEXITY PROMPT SENT ==========', file=sys.stderr)
        print(f'Title: {topic}', file=sys.stderr)
        print(f'Excerpt: {context_text}', file=sys.stderr)
        print(f'total_citations: {total_citations}', file=sys.stderr)
        print('Prompt:', prompt, file=sys.stderr)
        print('============================================\n', file=sys.stderr)
        output = call_perplexity_api(prompt)
        # Print the exact JSON returned by Perplexity
        print('\n========== PERPLEXITY RAW JSON RETURNED ==========', file=sys.stderr)
        print(json.dumps(output, indent=2), file=sys.stderr)
        print('============================================\n', file=sys.stderr)
        # Save raw Perplexity output to a temp file for inspection
        with tempfile.NamedTemporaryFile('w', delete=False, suffix='.json') as tmpf:
            json.dump(output, tmpf, indent=2)
            print(f'PERPLEXITY RAW OUTPUT SAVED TO: {tmpf.name}', file=sys.stderr)
        # Robustly parse all sources from Perplexity output
        def parse_multiple_sources(output):
            # If output is already a list of dicts (expected JSON format)
            if isinstance(output, list):
                return output
            # If output is a dict with url/title/is_paper, wrap in a list
            if isinstance(output, dict) and output.get('url'):
                return [output]
            # If output is a dict with 'results' key
            if isinstance(output, dict) and 'results' in output and isinstance(output['results'], list):
                return output['results']
            # Fallback: if output is a string, try to parse as JSON
            if isinstance(output, str):
                try:
                    parsed = json.loads(output)
                    if isinstance(parsed, list):
                        return parsed
                    elif isinstance(parsed, dict) and parsed.get('url'):
                        return [parsed]
                except json.JSONDecodeError:
                    # If JSON parsing fails, try the old text parsing as fallback
                    blocks = [b.strip() for b in re.split(r'\n\s*\n|(?=URL:)', output) if b.strip()]
                    results = []
                    for block in blocks:
                        url = re.search(r'URL:\s*(.+)', block)
                        title = re.search(r'TITLE:\s*(.+)', block)
                        is_paper = re.search(r'Research paper:\s*(yes|no)', block, re.IGNORECASE)
                        if url and title and is_paper:
                            results.append({
                                'url': url.group(1).strip(),
                                'title': title.group(1).strip(),
                                'is_paper': is_paper.group(1).strip().lower() == 'yes'
                            })
                    return results
            return []
        sources = parse_multiple_sources(output)
        for src in sources:
            url = src.get('url', '')
            title = src.get('title', '')
            is_paper = src.get('is_paper', None)
            def is_invalid(val):
                return not val or str(val).strip().lower() in ['n/a', 'na']
            if is_invalid(url) or is_invalid(title) or is_paper is None:
                print(f'CITEAI WARNING: Skipping blank or N/A Perplexity output for content.', file=sys.stderr)
                continue
            if is_paper:
                meta = fetch_paper_metadata(title, url)
                csl = build_csl_json(meta, idx=i) if meta else None
                if csl:
                    print(f'CSL JSON (OpenAlex/Crossref) for "{title}":', file=sys.stderr)
                    print(json.dumps(csl, indent=2), file=sys.stderr)
            else:
                _, csl = extract_article_metadata(url, idx=i)
            if csl and csl.get("title"):
                csl_items.append(csl)
    # After citation generation, print the number of valid Perplexity URLs received
    print(f'CITEAI INFO: Number of valid Perplexity URLs received: {len(csl_items)}', file=sys.stderr)
    csl_items = deduplicate_csl_items(csl_items)
    
    # Apply plan-based citation limits
    if plan == 'plus':
        csl_items = csl_items[:20]
    # pro: no limit

    # Output logic
    if output_mode == 'text':
        from filegen import format_citations_text
        formatted = format_citations_text(csl_items, citation_format)
        print(formatted)
    elif output_mode == 'html':
        from filegen import generate_html, sort_csl_items, build_formatted_entries, fix_missing_fields, clean_author_names, abbreviate_given_names
        valid_items = [item for item in csl_items if isinstance(item, dict) and item.get("title")]
        valid_items = fix_missing_fields(valid_items, citation_format)
        valid_items = clean_author_names(valid_items)
        if citation_format.lower() == "apa":
            valid_items = abbreviate_given_names(valid_items)
        sorted_items = sort_csl_items(valid_items, citation_format)
        entries = build_formatted_entries(sorted_items, citation_format)
        html = generate_html(entries, citation_format, sorted_items)
        print(html)
    else:
        from filegen import generate_citations_pdf
        pdf_path = generate_citations_pdf(csl_items, citation_format)
        print(json.dumps({"pdf_path": pdf_path}))

    # After deduplication and before output logic:
    print('\n===== COMPLETE CSL JSON OUTPUT =====', file=sys.stderr)
    print(json.dumps(csl_items, indent=2), file=sys.stderr)
    print('===== END CSL JSON OUTPUT =====\n', file=sys.stderr)


def test_citation_calculation():
    """Test function to see citation calculation output for different content types"""
    
    # Get user input for testing
    print("\n" + "="*60)
    print("CITATION CALCULATION TESTER")
    print("="*60)
    
    # Ask for plan
    plan = input("\nEnter plan (free/plus/pro): ").lower().strip()
    if plan not in ['free', 'plus', 'pro']:
        plan = 'free'
        print("Invalid plan, using 'free'")
    
    # Ask for content
    print("\nEnter your essay content (press Enter twice when done):")
    lines = []
    while True:
        line = input()
        if line == "" and lines and lines[-1] == "":
            break
        lines.append(line)
    
    content = "\n".join(lines[:-1])  # Remove the last empty line
    
    if not content.strip():
        print("No content provided, using sample content")
        content = "According to recent studies, the statistical analysis shows significant results."
    
    test_cases = [
        {
            "plan": plan,
            "content": content,
            "description": f"Your essay content ({plan} plan)"
        }
    ]
    
    print("\n" + "="*60)
    print("CITATION CALCULATION TEST RESULTS")
    print("="*60)
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n--- Test {i}: {test['description']} ---")
        print(f"Plan: {test['plan']}")
        print(f"Content: {test['content'][:100]}...")
        
        # Calculate word count
        word_count = _word_count_fast(test['content'])
        print(f"Word count: {word_count}")
        
        # Calculate research intensity
        intensity = _research_intensity_score(test['content'])
        print(f"Research intensity: {intensity:.3f}")
        
        # Calculate paper target
        paper_target = compute_paper_target(test['plan'], total_word_count=word_count, sample_text=test['content'])
        print(f"Paper target: {paper_target}")
        
        # Calculate citations for this chunk
        citations = num_citations(test['plan'], test['content'], 
                                paper_target=paper_target, 
                                total_word_count=word_count)
        print(f"Citations to generate: {citations}")
        
        # Show demand signals
        demand = _source_demand_signals(test['content'])
        print(f"Demand signals: {demand}")
        
        print("-" * 40)


if __name__ == "__main__":
    # Check if we want to run the test function
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        test_citation_calculation()
        sys.exit(0)
    
    parser = argparse.ArgumentParser(description="CiteAI Citation Generator")
    parser.add_argument('--input_json', type=str, help='Path to JSON file with title, content, citation_format, plan')
    parser.add_argument('--output_mode', type=str, choices=['text', 'pdf', 'html'], help='Output mode: text, pdf, or html')
    parser.add_argument('--test', action='store_true', help='Run citation calculation tests')
    args = parser.parse_args()