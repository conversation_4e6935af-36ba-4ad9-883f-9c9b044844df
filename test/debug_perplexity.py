#!/usr/bin/env python3

import requests
import json
import sys

def test_perplexity_api():
    """Test the Perplexity API directly"""
    
    endpoint = "https://api.perplexity.ai/chat/completions"
    api_key = "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"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test prompt - using the same format as the failing logs
    prompt = """a paper is being written about "Gender equality". find up to 1 original sources (no duplicates) which would have been used to generate the excerpt below. provide me with a url & title of the source and specify if it is a research paper or not. the fields you provide should be for a source that relatively matches the topic provided. give it in this format and do not provide any extra information besides this
URL: [url]
 TITLE: [title]
 Research paper: [yes/no]

excerpt: Moving on to the speech Emma delivered to launch the campaign back in September (https://www.youtube.com/watch?v=gkjW9PZBRfk). There were many layers of this speech that effectively highlight the campaign, which set it up to go viral once it was launched. The video from the United Nations' YouTube has only 458,495 views. However, there is a different video from YouTube user normaljean2, which has 6,243,504 views and on the video uploaded by HeForShe there are 1,418,431 views. Clearly, this video has been extremely effective in spreading the word for the campaign. The use of social media absolutely helped push this video to become as popular as it was. First off, the use of YouTube allowed a digital platform for the video to be uploaded to. Before YouTube was created many speeches like this had to be broadcast solely on television. In my opinion, YouTube is one of the better forms of social media"""
    
    # Test 1: Without JSON schema (simple)
    print("=== TEST 1: Simple format (no JSON schema) ===")
    simple_payload = {
        "model": "sonar",
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant that finds academic sources."
            },
            {
                "role": "user",
                "content": prompt
            }
        ]
    }
    
    try:
        resp = requests.post(endpoint, headers=headers, json=simple_payload, timeout=60)
        print(f"Status Code: {resp.status_code}")
        if resp.status_code == 200:
            data = resp.json()
            content = data["choices"][0]["message"]["content"]
            print(f"Response: {content}")
        else:
            print(f"Error: {resp.text}")
    except Exception as e:
        print(f"Exception: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test 2: With JSON schema (your current format)
    print("=== TEST 2: With JSON schema ===")
    json_schema = {
        "schema": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "url": {"type": "string"},
                    "title": {"type": "string"},
                    "is_paper": {"type": "boolean"}
                },
                "required": ["url", "title", "is_paper"]
            }
        }
    }

    schema_payload = {
        "model": "sonar",
        "messages": [
            {
                "role": "system",
                "content": "You are a concise and precise assistant. Only respond with a JSON array of objects exactly matching the requested schema. Do not include any additional text, summary, or notes."
            },
            {
                "role": "user",
                "content": prompt
            },
        ],
        "response_format": {
            "type": "json_schema",
            "json_schema": json_schema
        }
    }
    
    try:
        resp = requests.post(endpoint, headers=headers, json=schema_payload, timeout=60)
        print(f"Status Code: {resp.status_code}")
        if resp.status_code == 200:
            data = resp.json()
            content = data["choices"][0]["message"]["content"]
            print(f"Response: {content}")
            try:
                parsed = json.loads(content)
                print(f"Parsed JSON: {parsed}")
            except Exception as e:
                print(f"JSON Parse Error: {e}")
        else:
            print(f"Error: {resp.text}")
    except Exception as e:
        print(f"Exception: {e}")

if __name__ == "__main__":
    test_perplexity_api()